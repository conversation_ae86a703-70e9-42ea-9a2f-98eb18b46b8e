# 开发环境专用配置
# 使用方法: java -jar app.jar --spring.profiles.active=dev

server:
  port: 8080
  servlet:
    context-path: /api
  # 开发环境禁用SSL
  ssl:
    enabled: false

# 开发环境日志配置
logging:
  level:
    root: INFO
    org.meetsync: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 开发环境CORS配置
cors:
  allowed-origins: "http://localhost:3000,http://localhost:3016,http://127.0.0.1:3000,http://127.0.0.1:3016,http://localhost:8080,http://127.0.0.1:8080"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS,HEAD"
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600

# 开发环境数据库配置
spring:
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

# 开发环境媒体服务器配置
meetsync:
  media-server:
    url: "ws://localhost:3017/java-ws"
    auth-token: "media-server-secret-token"
    reconnect-interval: 5000
    timeout: 30000
    health-check:
      enabled: true
      interval: 60000
