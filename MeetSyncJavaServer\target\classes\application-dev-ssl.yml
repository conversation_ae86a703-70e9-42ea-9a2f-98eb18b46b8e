# 开发环境 SSL 配置
server:
  port: 8443  # HTTPS 端口
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: meetsync123
    key-store-type: PKCS12
    key-alias: meetsync
    protocol: TLS
    enabled-protocols: TLSv1.2,TLSv1.3
    # 开发环境：接受自签名证书
    client-auth: none

# CORS 配置 - 支持 HTTPS
cors:
  allowed-origins: "https://localhost:3016,https://127.0.0.1:3016,http://localhost:3016,http://127.0.0.1:3016,https://localhost:3000,https://127.0.0.1:3000,http://localhost:3000,http://127.0.0.1:3000"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS,HEAD"
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600

# 媒体服务器配置 - 支持 WSS
meetsync:
  media-server:
    # 如果媒体服务器也支持 WSS，可以修改为 wss://
    url: ${MEDIA_SERVER_WS_URL:ws://localhost:3017/java-ws}
    auth-token: ${MEDIA_SERVER_TOKEN:media-server-secret-token}
    reconnect-interval: 5000
    timeout: 30000

# 日志配置 - 开发环境详细日志
logging:
  level:
    root: INFO
    org.meetsync: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.springframework.web.socket: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
